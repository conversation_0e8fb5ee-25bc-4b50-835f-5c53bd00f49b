<!--
    FluentSystemDesign WPF控件库 - 色彩定义资源字典
    
    此文件定义了基于Microsoft Fluent Design System的完整色彩体系，
    包括主题色、中性色、语义色和状态色的完整色彩层级。
    
    色彩系统特点：
    1. 基于Microsoft Fluent Design System设计语言
    2. 支持深色和浅色主题
    3. 符合WCAG无障碍对比度标准
    4. 提供完整的色彩层级（50-900渐变）
    5. 包含语义色彩和状态色彩
    
    使用方法：
    - 在应用程序中引用此资源字典
    - 通过StaticResource或DynamicResource引用色彩
    - 配合Brushes.xaml使用获得最佳效果
-->

<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- ================================ -->
    <!-- 主题色彩 (Primary Colors)        -->
    <!-- ================================ -->
    
    <!-- 主色调 - 蓝色系 (基于Fluent Design的默认蓝色) -->
    <!-- 用于主要操作按钮、链接、选中状态等 -->
    <Color x:Key="PrimaryColor50">#E3F2FD</Color>   <!-- 最浅的主色调，用于背景高亮 -->
    <Color x:Key="PrimaryColor100">#BBDEFB</Color>  <!-- 浅色主色调，用于悬浮状态 -->
    <Color x:Key="PrimaryColor200">#90CAF9</Color>  <!-- 较浅主色调 -->
    <Color x:Key="PrimaryColor300">#64B5F6</Color>  <!-- 中浅主色调 -->
    <Color x:Key="PrimaryColor400">#42A5F5</Color>  <!-- 中等主色调 -->
    <Color x:Key="PrimaryColor500">#2196F3</Color>  <!-- 标准主色调，最常用的主色 -->
    <Color x:Key="PrimaryColor600">#1E88E5</Color>  <!-- 中深主色调 -->
    <Color x:Key="PrimaryColor700">#1976D2</Color>  <!-- 较深主色调 -->
    <Color x:Key="PrimaryColor800">#1565C0</Color>  <!-- 深色主色调 -->
    <Color x:Key="PrimaryColor900">#0D47A1</Color>  <!-- 最深主色调，用于强调 -->

    <!-- 辅助色调 - 青色系 -->
    <!-- 用于辅助信息、次要操作等 -->
    <Color x:Key="SecondaryColor50">#E0F2F1</Color>
    <Color x:Key="SecondaryColor100">#B2DFDB</Color>
    <Color x:Key="SecondaryColor200">#80CBC4</Color>
    <Color x:Key="SecondaryColor300">#4DB6AC</Color>
    <Color x:Key="SecondaryColor400">#26A69A</Color>
    <Color x:Key="SecondaryColor500">#009688</Color>  <!-- 标准辅助色 -->
    <Color x:Key="SecondaryColor600">#00897B</Color>
    <Color x:Key="SecondaryColor700">#00796B</Color>
    <Color x:Key="SecondaryColor800">#00695C</Color>
    <Color x:Key="SecondaryColor900">#004D40</Color>

    <!-- 强调色调 - 橙色系 -->
    <!-- 用于警告、通知、特殊强调等 -->
    <Color x:Key="AccentColor50">#FFF3E0</Color>
    <Color x:Key="AccentColor100">#FFE0B2</Color>
    <Color x:Key="AccentColor200">#FFCC80</Color>
    <Color x:Key="AccentColor300">#FFB74D</Color>
    <Color x:Key="AccentColor400">#FFA726</Color>
    <Color x:Key="AccentColor500">#FF9800</Color>    <!-- 标准强调色 -->
    <Color x:Key="AccentColor600">#FB8C00</Color>
    <Color x:Key="AccentColor700">#F57C00</Color>
    <Color x:Key="AccentColor800">#EF6C00</Color>
    <Color x:Key="AccentColor900">#E65100</Color>

    <!-- ================================ -->
    <!-- 中性色彩 (Neutral Colors)        -->
    <!-- ================================ -->
    
    <!-- 灰色系 - 用于文本、边框、背景等 -->
    <Color x:Key="NeutralColor50">#FAFAFA</Color>    <!-- 最浅灰色，用于背景 -->
    <Color x:Key="NeutralColor100">#F5F5F5</Color>   <!-- 浅灰色背景 -->
    <Color x:Key="NeutralColor200">#EEEEEE</Color>   <!-- 分割线、边框 -->
    <Color x:Key="NeutralColor300">#E0E0E0</Color>   <!-- 禁用状态边框 -->
    <Color x:Key="NeutralColor400">#BDBDBD</Color>   <!-- 占位符文本 -->
    <Color x:Key="NeutralColor500">#9E9E9E</Color>   <!-- 次要文本 -->
    <Color x:Key="NeutralColor600">#757575</Color>   <!-- 辅助文本 -->
    <Color x:Key="NeutralColor700">#616161</Color>   <!-- 主要文本（浅色主题） -->
    <Color x:Key="NeutralColor800">#424242</Color>   <!-- 深色文本 -->
    <Color x:Key="NeutralColor900">#212121</Color>   <!-- 最深文本色 -->

    <!-- 纯色 -->
    <Color x:Key="WhiteColor">#FFFFFF</Color>        <!-- 纯白色 -->
    <Color x:Key="BlackColor">#000000</Color>        <!-- 纯黑色 -->

    <!-- ================================ -->
    <!-- 语义色彩 (Semantic Colors)       -->
    <!-- ================================ -->
    
    <!-- 成功色彩 - 绿色系 -->
    <!-- 用于成功状态、确认操作、正面反馈等 -->
    <Color x:Key="SuccessColor50">#E8F5E8</Color>
    <Color x:Key="SuccessColor100">#C8E6C9</Color>
    <Color x:Key="SuccessColor200">#A5D6A7</Color>
    <Color x:Key="SuccessColor300">#81C784</Color>
    <Color x:Key="SuccessColor400">#66BB6A</Color>
    <Color x:Key="SuccessColor500">#4CAF50</Color>    <!-- 标准成功色 -->
    <Color x:Key="SuccessColor600">#43A047</Color>
    <Color x:Key="SuccessColor700">#388E3C</Color>
    <Color x:Key="SuccessColor800">#2E7D32</Color>
    <Color x:Key="SuccessColor900">#1B5E20</Color>

    <!-- 警告色彩 - 黄色系 -->
    <!-- 用于警告状态、注意事项、需要用户注意的信息等 -->
    <Color x:Key="WarningColor50">#FFFDE7</Color>
    <Color x:Key="WarningColor100">#FFF9C4</Color>
    <Color x:Key="WarningColor200">#FFF59D</Color>
    <Color x:Key="WarningColor300">#FFF176</Color>
    <Color x:Key="WarningColor400">#FFEE58</Color>
    <Color x:Key="WarningColor500">#FFEB3B</Color>    <!-- 标准警告色 -->
    <Color x:Key="WarningColor600">#FDD835</Color>
    <Color x:Key="WarningColor700">#FBC02D</Color>
    <Color x:Key="WarningColor800">#F9A825</Color>
    <Color x:Key="WarningColor900">#F57F17</Color>

    <!-- 错误色彩 - 红色系 -->
    <!-- 用于错误状态、删除操作、危险操作等 -->
    <Color x:Key="ErrorColor50">#FFEBEE</Color>
    <Color x:Key="ErrorColor100">#FFCDD2</Color>
    <Color x:Key="ErrorColor200">#EF9A9A</Color>
    <Color x:Key="ErrorColor300">#E57373</Color>
    <Color x:Key="ErrorColor400">#EF5350</Color>
    <Color x:Key="ErrorColor500">#F44336</Color>      <!-- 标准错误色 -->
    <Color x:Key="ErrorColor600">#E53935</Color>
    <Color x:Key="ErrorColor700">#D32F2F</Color>
    <Color x:Key="ErrorColor800">#C62828</Color>
    <Color x:Key="ErrorColor900">#B71C1C</Color>

    <!-- 信息色彩 - 蓝色系（与主色调略有不同） -->
    <!-- 用于信息提示、帮助文本、一般性通知等 -->
    <Color x:Key="InfoColor50">#E1F5FE</Color>
    <Color x:Key="InfoColor100">#B3E5FC</Color>
    <Color x:Key="InfoColor200">#81D4FA</Color>
    <Color x:Key="InfoColor300">#4FC3F7</Color>
    <Color x:Key="InfoColor400">#29B6F6</Color>
    <Color x:Key="InfoColor500">#03A9F4</Color>       <!-- 标准信息色 -->
    <Color x:Key="InfoColor600">#039BE5</Color>
    <Color x:Key="InfoColor700">#0288D1</Color>
    <Color x:Key="InfoColor800">#0277BD</Color>
    <Color x:Key="InfoColor900">#01579B</Color>

    <!-- ================================ -->
    <!-- 文本色彩 (Text Colors)           -->
    <!-- ================================ -->
    
    <!-- 浅色主题文本色彩 -->
    <Color x:Key="TextPrimaryLight">#212121</Color>      <!-- 主要文本（浅色主题） -->
    <Color x:Key="TextSecondaryLight">#757575</Color>    <!-- 次要文本（浅色主题） -->
    <Color x:Key="TextDisabledLight">#BDBDBD</Color>     <!-- 禁用文本（浅色主题） -->
    <Color x:Key="TextHintLight">#9E9E9E</Color>         <!-- 提示文本（浅色主题） -->

    <!-- 深色主题文本色彩 -->
    <Color x:Key="TextPrimaryDark">#FFFFFF</Color>       <!-- 主要文本（深色主题） -->
    <Color x:Key="TextSecondaryDark">#B3FFFFFF</Color>   <!-- 次要文本（深色主题，70%透明度） -->
    <Color x:Key="TextDisabledDark">#4DFFFFFF</Color>    <!-- 禁用文本（深色主题，30%透明度） -->
    <Color x:Key="TextHintDark">#80FFFFFF</Color>        <!-- 提示文本（深色主题，50%透明度） -->

    <!-- ================================ -->
    <!-- 背景色彩 (Background Colors)     -->
    <!-- ================================ -->
    
    <!-- 浅色主题背景色彩 -->
    <Color x:Key="BackgroundPrimaryLight">#FFFFFF</Color>    <!-- 主背景（浅色主题） -->
    <Color x:Key="BackgroundSecondaryLight">#FAFAFA</Color>  <!-- 次背景（浅色主题） -->
    <Color x:Key="BackgroundCardLight">#FFFFFF</Color>       <!-- 卡片背景（浅色主题） -->
    <Color x:Key="BackgroundHoverLight">#F5F5F5</Color>      <!-- 悬浮背景（浅色主题） -->
    <Color x:Key="BackgroundPressedLight">#EEEEEE</Color>    <!-- 按下背景（浅色主题） -->

    <!-- 深色主题背景色彩 -->
    <Color x:Key="BackgroundPrimaryDark">#121212</Color>     <!-- 主背景（深色主题） -->
    <Color x:Key="BackgroundSecondaryDark">#1E1E1E</Color>   <!-- 次背景（深色主题） -->
    <Color x:Key="BackgroundCardDark">#2D2D2D</Color>        <!-- 卡片背景（深色主题） -->
    <Color x:Key="BackgroundHoverDark">#383838</Color>       <!-- 悬浮背景（深色主题） -->
    <Color x:Key="BackgroundPressedDark">#424242</Color>     <!-- 按下背景（深色主题） -->

    <!-- ================================ -->
    <!-- 边框色彩 (Border Colors)         -->
    <!-- ================================ -->
    
    <!-- 浅色主题边框色彩 -->
    <Color x:Key="BorderPrimaryLight">#E0E0E0</Color>        <!-- 主边框（浅色主题） -->
    <Color x:Key="BorderSecondaryLight">#EEEEEE</Color>      <!-- 次边框（浅色主题） -->
    <Color x:Key="BorderFocusLight">#2196F3</Color>          <!-- 焦点边框（浅色主题） -->

    <!-- 深色主题边框色彩 -->
    <Color x:Key="BorderPrimaryDark">#424242</Color>         <!-- 主边框（深色主题） -->
    <Color x:Key="BorderSecondaryDark">#383838</Color>       <!-- 次边框（深色主题） -->
    <Color x:Key="BorderFocusDark">#64B5F6</Color>           <!-- 焦点边框（深色主题） -->

    <!-- ================================ -->
    <!-- 阴影色彩 (Shadow Colors)         -->
    <!-- ================================ -->
    
    <!-- 用于投影效果的色彩 -->
    <Color x:Key="ShadowLight">#1A000000</Color>             <!-- 浅色阴影（10%透明度） -->
    <Color x:Key="ShadowMedium">#33000000</Color>            <!-- 中等阴影（20%透明度） -->
    <Color x:Key="ShadowDark">#4D000000</Color>              <!-- 深色阴影（30%透明度） -->

</ResourceDictionary>

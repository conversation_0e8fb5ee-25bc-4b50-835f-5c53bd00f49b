<!--
    FluentSystemDesign WPF控件库 - 通用资源字典
    
    此文件是控件库的主资源字典，整合了所有主题相关的资源。
    应用程序只需引用此文件即可获得完整的色彩系统支持。
    
    包含的资源：
    1. Colors.xaml - 色彩定义
    2. Brushes.xaml - 画刷资源
    3. ThemeManager.xaml - 主题管理
    4. 未来的控件样式资源字典
    
    使用方法：
    在App.xaml中引用此资源字典：
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/FluentDesignWPF;component/Themes/Generic.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Application.Resources>
-->

<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- ================================ -->
    <!-- 合并所有主题资源字典               -->
    <!-- ================================ -->
    
    <ResourceDictionary.MergedDictionaries>

        <!-- 基础色彩定义 -->
        <ResourceDictionary Source="Colors.xaml"/>

        <!-- 画刷资源 -->
        <ResourceDictionary Source="Brushes.xaml"/>

        <!-- 默认浅色主题 -->
        <ResourceDictionary Source="LightTheme.xaml"/>

        <!-- 未来的控件样式资源字典将在这里添加 -->
        <!-- <ResourceDictionary Source="Controls/Button.xaml"/> -->
        <!-- <ResourceDictionary Source="Controls/TextBox.xaml"/> -->
        <!-- <ResourceDictionary Source="Controls/ComboBox.xaml"/> -->

    </ResourceDictionary.MergedDictionaries>

    <!-- ================================ -->
    <!-- 全局样式和资源                    -->
    <!-- ================================ -->
    
    <!-- 默认字体设置 -->
    <FontFamily x:Key="DefaultFontFamily">Segoe UI, Microsoft YaHei UI, sans-serif</FontFamily>
    <FontFamily x:Key="MonospaceFontFamily">Consolas, Courier New, monospace</FontFamily>
    
    <!-- 默认字体大小 -->
    <sys:Double x:Key="FontSizeSmall" xmlns:sys="clr-namespace:System;assembly=mscorlib">12</sys:Double>
    <sys:Double x:Key="FontSizeNormal" xmlns:sys="clr-namespace:System;assembly=mscorlib">14</sys:Double>
    <sys:Double x:Key="FontSizeLarge" xmlns:sys="clr-namespace:System;assembly=mscorlib">16</sys:Double>
    <sys:Double x:Key="FontSizeExtraLarge" xmlns:sys="clr-namespace:System;assembly=mscorlib">20</sys:Double>
    <sys:Double x:Key="FontSizeTitle" xmlns:sys="clr-namespace:System;assembly=mscorlib">24</sys:Double>
    <sys:Double x:Key="FontSizeHeading" xmlns:sys="clr-namespace:System;assembly=mscorlib">32</sys:Double>

    <!-- 默认间距设置 -->
    <sys:Double x:Key="SpacingExtraSmall" xmlns:sys="clr-namespace:System;assembly=mscorlib">4</sys:Double>
    <sys:Double x:Key="SpacingSmall" xmlns:sys="clr-namespace:System;assembly=mscorlib">8</sys:Double>
    <sys:Double x:Key="SpacingNormal" xmlns:sys="clr-namespace:System;assembly=mscorlib">12</sys:Double>
    <sys:Double x:Key="SpacingMedium" xmlns:sys="clr-namespace:System;assembly=mscorlib">16</sys:Double>
    <sys:Double x:Key="SpacingLarge" xmlns:sys="clr-namespace:System;assembly=mscorlib">24</sys:Double>
    <sys:Double x:Key="SpacingExtraLarge" xmlns:sys="clr-namespace:System;assembly=mscorlib">32</sys:Double>

    <!-- 默认圆角设置 -->
    <CornerRadius x:Key="CornerRadiusSmall">2</CornerRadius>
    <CornerRadius x:Key="CornerRadiusNormal">4</CornerRadius>
    <CornerRadius x:Key="CornerRadiusMedium">6</CornerRadius>
    <CornerRadius x:Key="CornerRadiusLarge">8</CornerRadius>
    <CornerRadius x:Key="CornerRadiusExtraLarge">12</CornerRadius>

    <!-- 默认厚度设置 -->
    <Thickness x:Key="BorderThicknessNormal">1</Thickness>
    <Thickness x:Key="BorderThicknessThick">2</Thickness>
    <Thickness x:Key="PaddingSmall">4</Thickness>
    <Thickness x:Key="PaddingNormal">8</Thickness>
    <Thickness x:Key="PaddingMedium">12</Thickness>
    <Thickness x:Key="PaddingLarge">16</Thickness>
    <Thickness x:Key="MarginSmall">4</Thickness>
    <Thickness x:Key="MarginNormal">8</Thickness>
    <Thickness x:Key="MarginMedium">12</Thickness>
    <Thickness x:Key="MarginLarge">16</Thickness>

    <!-- 动画持续时间 -->
    <Duration x:Key="AnimationDurationFast">0:0:0.15</Duration>
    <Duration x:Key="AnimationDurationNormal">0:0:0.25</Duration>
    <Duration x:Key="AnimationDurationSlow">0:0:0.35</Duration>

    <!-- 阴影效果 -->
    <DropShadowEffect x:Key="ShadowDepth1" 
                      Color="{StaticResource ShadowLight}" 
                      Direction="270" 
                      ShadowDepth="1" 
                      BlurRadius="3" 
                      Opacity="0.3"/>
    
    <DropShadowEffect x:Key="ShadowDepth2" 
                      Color="{StaticResource ShadowMedium}" 
                      Direction="270" 
                      ShadowDepth="2" 
                      BlurRadius="6" 
                      Opacity="0.3"/>
    
    <DropShadowEffect x:Key="ShadowDepth3" 
                      Color="{StaticResource ShadowDark}" 
                      Direction="270" 
                      ShadowDepth="4" 
                      BlurRadius="12" 
                      Opacity="0.3"/>

    <!-- ================================ -->
    <!-- 通用样式                          -->
    <!-- ================================ -->
    
    <!-- 默认窗口样式 -->
    <Style x:Key="FluentWindow" TargetType="Window">
        <Setter Property="Background" Value="{DynamicResource ThemeBackgroundPrimaryBrush}"/>
        <Setter Property="Foreground" Value="{DynamicResource ThemeTextPrimaryBrush}"/>
        <Setter Property="FontFamily" Value="{StaticResource DefaultFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource FontSizeNormal}"/>
        <Setter Property="UseLayoutRounding" Value="True"/>
        <Setter Property="TextOptions.TextFormattingMode" Value="Display"/>
        <Setter Property="TextOptions.TextRenderingMode" Value="ClearType"/>
    </Style>

    <!-- 默认用户控件样式 -->
    <Style x:Key="FluentUserControl" TargetType="UserControl">
        <Setter Property="Background" Value="{DynamicResource ThemeBackgroundPrimaryBrush}"/>
        <Setter Property="Foreground" Value="{DynamicResource ThemeTextPrimaryBrush}"/>
        <Setter Property="FontFamily" Value="{StaticResource DefaultFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource FontSizeNormal}"/>
        <Setter Property="UseLayoutRounding" Value="True"/>
        <Setter Property="TextOptions.TextFormattingMode" Value="Display"/>
        <Setter Property="TextOptions.TextRenderingMode" Value="ClearType"/>
    </Style>

    <!-- 默认文本块样式 -->
    <Style x:Key="FluentTextBlock" TargetType="TextBlock">
        <Setter Property="Foreground" Value="{DynamicResource ThemeTextPrimaryBrush}"/>
        <Setter Property="FontFamily" Value="{StaticResource DefaultFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource FontSizeNormal}"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
        <Setter Property="TextTrimming" Value="CharacterEllipsis"/>
    </Style>

    <!-- 标题文本样式 -->
    <Style x:Key="FluentTitleTextBlock" TargetType="TextBlock" BasedOn="{StaticResource FluentTextBlock}">
        <Setter Property="FontSize" Value="{StaticResource FontSizeTitle}"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Margin" Value="{StaticResource MarginMedium}"/>
    </Style>

    <!-- 副标题文本样式 -->
    <Style x:Key="FluentSubtitleTextBlock" TargetType="TextBlock" BasedOn="{StaticResource FluentTextBlock}">
        <Setter Property="FontSize" Value="{StaticResource FontSizeLarge}"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="Foreground" Value="{DynamicResource ThemeTextSecondaryBrush}"/>
        <Setter Property="Margin" Value="{StaticResource MarginNormal}"/>
    </Style>

    <!-- 说明文本样式 -->
    <Style x:Key="FluentCaptionTextBlock" TargetType="TextBlock" BasedOn="{StaticResource FluentTextBlock}">
        <Setter Property="FontSize" Value="{StaticResource FontSizeSmall}"/>
        <Setter Property="Foreground" Value="{DynamicResource ThemeTextSecondaryBrush}"/>
    </Style>

</ResourceDictionary>

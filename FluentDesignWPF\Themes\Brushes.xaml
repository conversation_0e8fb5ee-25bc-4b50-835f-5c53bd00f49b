<!--
    FluentSystemDesign WPF控件库 - 画刷资源字典
    
    此文件基于Colors.xaml中定义的色彩创建对应的SolidColorBrush资源，
    为WPF控件提供可直接使用的画刷资源。
    
    画刷系统特点：
    1. 基于Colors.xaml中的色彩定义
    2. 支持主题切换（通过DynamicResource）
    3. 提供完整的画刷层级
    4. 包含语义画刷和状态画刷
    5. 优化的命名规范便于使用
    
    使用方法：
    - 在控件样式中通过StaticResource或DynamicResource引用
    - 建议使用DynamicResource以支持主题切换
    - 配合Colors.xaml使用获得最佳效果
-->

<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- 引用Colors.xaml资源字典 -->
    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="Colors.xaml"/>
    </ResourceDictionary.MergedDictionaries>

    <!-- ================================ -->
    <!-- 主题画刷 (Primary Brushes)        -->
    <!-- ================================ -->
    
    <!-- 主色调画刷 - 蓝色系 -->
    <SolidColorBrush x:Key="PrimaryBrush50" Color="{StaticResource PrimaryColor50}"/>
    <SolidColorBrush x:Key="PrimaryBrush100" Color="{StaticResource PrimaryColor100}"/>
    <SolidColorBrush x:Key="PrimaryBrush200" Color="{StaticResource PrimaryColor200}"/>
    <SolidColorBrush x:Key="PrimaryBrush300" Color="{StaticResource PrimaryColor300}"/>
    <SolidColorBrush x:Key="PrimaryBrush400" Color="{StaticResource PrimaryColor400}"/>
    <SolidColorBrush x:Key="PrimaryBrush500" Color="{StaticResource PrimaryColor500}"/>
    <SolidColorBrush x:Key="PrimaryBrush600" Color="{StaticResource PrimaryColor600}"/>
    <SolidColorBrush x:Key="PrimaryBrush700" Color="{StaticResource PrimaryColor700}"/>
    <SolidColorBrush x:Key="PrimaryBrush800" Color="{StaticResource PrimaryColor800}"/>
    <SolidColorBrush x:Key="PrimaryBrush900" Color="{StaticResource PrimaryColor900}"/>

    <!-- 常用主色调画刷别名 -->
    <SolidColorBrush x:Key="PrimaryBrush" Color="{StaticResource PrimaryColor500}"/>
    <SolidColorBrush x:Key="PrimaryLightBrush" Color="{StaticResource PrimaryColor300}"/>
    <SolidColorBrush x:Key="PrimaryDarkBrush" Color="{StaticResource PrimaryColor700}"/>

    <!-- 辅助色调画刷 - 青色系 -->
    <SolidColorBrush x:Key="SecondaryBrush50" Color="{StaticResource SecondaryColor50}"/>
    <SolidColorBrush x:Key="SecondaryBrush100" Color="{StaticResource SecondaryColor100}"/>
    <SolidColorBrush x:Key="SecondaryBrush200" Color="{StaticResource SecondaryColor200}"/>
    <SolidColorBrush x:Key="SecondaryBrush300" Color="{StaticResource SecondaryColor300}"/>
    <SolidColorBrush x:Key="SecondaryBrush400" Color="{StaticResource SecondaryColor400}"/>
    <SolidColorBrush x:Key="SecondaryBrush500" Color="{StaticResource SecondaryColor500}"/>
    <SolidColorBrush x:Key="SecondaryBrush600" Color="{StaticResource SecondaryColor600}"/>
    <SolidColorBrush x:Key="SecondaryBrush700" Color="{StaticResource SecondaryColor700}"/>
    <SolidColorBrush x:Key="SecondaryBrush800" Color="{StaticResource SecondaryColor800}"/>
    <SolidColorBrush x:Key="SecondaryBrush900" Color="{StaticResource SecondaryColor900}"/>

    <!-- 常用辅助色画刷别名 -->
    <SolidColorBrush x:Key="SecondaryBrush" Color="{StaticResource SecondaryColor500}"/>
    <SolidColorBrush x:Key="SecondaryLightBrush" Color="{StaticResource SecondaryColor300}"/>
    <SolidColorBrush x:Key="SecondaryDarkBrush" Color="{StaticResource SecondaryColor700}"/>

    <!-- 强调色调画刷 - 橙色系 -->
    <SolidColorBrush x:Key="AccentBrush50" Color="{StaticResource AccentColor50}"/>
    <SolidColorBrush x:Key="AccentBrush100" Color="{StaticResource AccentColor100}"/>
    <SolidColorBrush x:Key="AccentBrush200" Color="{StaticResource AccentColor200}"/>
    <SolidColorBrush x:Key="AccentBrush300" Color="{StaticResource AccentColor300}"/>
    <SolidColorBrush x:Key="AccentBrush400" Color="{StaticResource AccentColor400}"/>
    <SolidColorBrush x:Key="AccentBrush500" Color="{StaticResource AccentColor500}"/>
    <SolidColorBrush x:Key="AccentBrush600" Color="{StaticResource AccentColor600}"/>
    <SolidColorBrush x:Key="AccentBrush700" Color="{StaticResource AccentColor700}"/>
    <SolidColorBrush x:Key="AccentBrush800" Color="{StaticResource AccentColor800}"/>
    <SolidColorBrush x:Key="AccentBrush900" Color="{StaticResource AccentColor900}"/>

    <!-- 常用强调色画刷别名 -->
    <SolidColorBrush x:Key="AccentBrush" Color="{StaticResource AccentColor500}"/>
    <SolidColorBrush x:Key="AccentLightBrush" Color="{StaticResource AccentColor300}"/>
    <SolidColorBrush x:Key="AccentDarkBrush" Color="{StaticResource AccentColor700}"/>

    <!-- ================================ -->
    <!-- 中性画刷 (Neutral Brushes)        -->
    <!-- ================================ -->
    
    <!-- 灰色系画刷 -->
    <SolidColorBrush x:Key="NeutralBrush50" Color="{StaticResource NeutralColor50}"/>
    <SolidColorBrush x:Key="NeutralBrush100" Color="{StaticResource NeutralColor100}"/>
    <SolidColorBrush x:Key="NeutralBrush200" Color="{StaticResource NeutralColor200}"/>
    <SolidColorBrush x:Key="NeutralBrush300" Color="{StaticResource NeutralColor300}"/>
    <SolidColorBrush x:Key="NeutralBrush400" Color="{StaticResource NeutralColor400}"/>
    <SolidColorBrush x:Key="NeutralBrush500" Color="{StaticResource NeutralColor500}"/>
    <SolidColorBrush x:Key="NeutralBrush600" Color="{StaticResource NeutralColor600}"/>
    <SolidColorBrush x:Key="NeutralBrush700" Color="{StaticResource NeutralColor700}"/>
    <SolidColorBrush x:Key="NeutralBrush800" Color="{StaticResource NeutralColor800}"/>
    <SolidColorBrush x:Key="NeutralBrush900" Color="{StaticResource NeutralColor900}"/>

    <!-- 纯色画刷 -->
    <SolidColorBrush x:Key="WhiteBrush" Color="{StaticResource WhiteColor}"/>
    <SolidColorBrush x:Key="BlackBrush" Color="{StaticResource BlackColor}"/>

    <!-- ================================ -->
    <!-- 语义画刷 (Semantic Brushes)       -->
    <!-- ================================ -->
    
    <!-- 成功画刷 - 绿色系 -->
    <SolidColorBrush x:Key="SuccessBrush50" Color="{StaticResource SuccessColor50}"/>
    <SolidColorBrush x:Key="SuccessBrush100" Color="{StaticResource SuccessColor100}"/>
    <SolidColorBrush x:Key="SuccessBrush200" Color="{StaticResource SuccessColor200}"/>
    <SolidColorBrush x:Key="SuccessBrush300" Color="{StaticResource SuccessColor300}"/>
    <SolidColorBrush x:Key="SuccessBrush400" Color="{StaticResource SuccessColor400}"/>
    <SolidColorBrush x:Key="SuccessBrush500" Color="{StaticResource SuccessColor500}"/>
    <SolidColorBrush x:Key="SuccessBrush600" Color="{StaticResource SuccessColor600}"/>
    <SolidColorBrush x:Key="SuccessBrush700" Color="{StaticResource SuccessColor700}"/>
    <SolidColorBrush x:Key="SuccessBrush800" Color="{StaticResource SuccessColor800}"/>
    <SolidColorBrush x:Key="SuccessBrush900" Color="{StaticResource SuccessColor900}"/>

    <!-- 常用成功色画刷别名 -->
    <SolidColorBrush x:Key="SuccessBrush" Color="{StaticResource SuccessColor500}"/>
    <SolidColorBrush x:Key="SuccessLightBrush" Color="{StaticResource SuccessColor300}"/>
    <SolidColorBrush x:Key="SuccessDarkBrush" Color="{StaticResource SuccessColor700}"/>

    <!-- 警告画刷 - 黄色系 -->
    <SolidColorBrush x:Key="WarningBrush50" Color="{StaticResource WarningColor50}"/>
    <SolidColorBrush x:Key="WarningBrush100" Color="{StaticResource WarningColor100}"/>
    <SolidColorBrush x:Key="WarningBrush200" Color="{StaticResource WarningColor200}"/>
    <SolidColorBrush x:Key="WarningBrush300" Color="{StaticResource WarningColor300}"/>
    <SolidColorBrush x:Key="WarningBrush400" Color="{StaticResource WarningColor400}"/>
    <SolidColorBrush x:Key="WarningBrush500" Color="{StaticResource WarningColor500}"/>
    <SolidColorBrush x:Key="WarningBrush600" Color="{StaticResource WarningColor600}"/>
    <SolidColorBrush x:Key="WarningBrush700" Color="{StaticResource WarningColor700}"/>
    <SolidColorBrush x:Key="WarningBrush800" Color="{StaticResource WarningColor800}"/>
    <SolidColorBrush x:Key="WarningBrush900" Color="{StaticResource WarningColor900}"/>

    <!-- 常用警告色画刷别名 -->
    <SolidColorBrush x:Key="WarningBrush" Color="{StaticResource WarningColor500}"/>
    <SolidColorBrush x:Key="WarningLightBrush" Color="{StaticResource WarningColor300}"/>
    <SolidColorBrush x:Key="WarningDarkBrush" Color="{StaticResource WarningColor700}"/>

    <!-- 错误画刷 - 红色系 -->
    <SolidColorBrush x:Key="ErrorBrush50" Color="{StaticResource ErrorColor50}"/>
    <SolidColorBrush x:Key="ErrorBrush100" Color="{StaticResource ErrorColor100}"/>
    <SolidColorBrush x:Key="ErrorBrush200" Color="{StaticResource ErrorColor200}"/>
    <SolidColorBrush x:Key="ErrorBrush300" Color="{StaticResource ErrorColor300}"/>
    <SolidColorBrush x:Key="ErrorBrush400" Color="{StaticResource ErrorColor400}"/>
    <SolidColorBrush x:Key="ErrorBrush500" Color="{StaticResource ErrorColor500}"/>
    <SolidColorBrush x:Key="ErrorBrush600" Color="{StaticResource ErrorColor600}"/>
    <SolidColorBrush x:Key="ErrorBrush700" Color="{StaticResource ErrorColor700}"/>
    <SolidColorBrush x:Key="ErrorBrush800" Color="{StaticResource ErrorColor800}"/>
    <SolidColorBrush x:Key="ErrorBrush900" Color="{StaticResource ErrorColor900}"/>

    <!-- 常用错误色画刷别名 -->
    <SolidColorBrush x:Key="ErrorBrush" Color="{StaticResource ErrorColor500}"/>
    <SolidColorBrush x:Key="ErrorLightBrush" Color="{StaticResource ErrorColor300}"/>
    <SolidColorBrush x:Key="ErrorDarkBrush" Color="{StaticResource ErrorColor700}"/>

    <!-- 信息画刷 - 蓝色系 -->
    <SolidColorBrush x:Key="InfoBrush50" Color="{StaticResource InfoColor50}"/>
    <SolidColorBrush x:Key="InfoBrush100" Color="{StaticResource InfoColor100}"/>
    <SolidColorBrush x:Key="InfoBrush200" Color="{StaticResource InfoColor200}"/>
    <SolidColorBrush x:Key="InfoBrush300" Color="{StaticResource InfoColor300}"/>
    <SolidColorBrush x:Key="InfoBrush400" Color="{StaticResource InfoColor400}"/>
    <SolidColorBrush x:Key="InfoBrush500" Color="{StaticResource InfoColor500}"/>
    <SolidColorBrush x:Key="InfoBrush600" Color="{StaticResource InfoColor600}"/>
    <SolidColorBrush x:Key="InfoBrush700" Color="{StaticResource InfoColor700}"/>
    <SolidColorBrush x:Key="InfoBrush800" Color="{StaticResource InfoColor800}"/>
    <SolidColorBrush x:Key="InfoBrush900" Color="{StaticResource InfoColor900}"/>

    <!-- 常用信息色画刷别名 -->
    <SolidColorBrush x:Key="InfoBrush" Color="{StaticResource InfoColor500}"/>
    <SolidColorBrush x:Key="InfoLightBrush" Color="{StaticResource InfoColor300}"/>
    <SolidColorBrush x:Key="InfoDarkBrush" Color="{StaticResource InfoColor700}"/>

    <!-- ================================ -->
    <!-- 文本画刷 (Text Brushes)           -->
    <!-- ================================ -->

    <!-- 浅色主题文本画刷 -->
    <SolidColorBrush x:Key="TextPrimaryLightBrush" Color="{StaticResource TextPrimaryLight}"/>
    <SolidColorBrush x:Key="TextSecondaryLightBrush" Color="{StaticResource TextSecondaryLight}"/>
    <SolidColorBrush x:Key="TextDisabledLightBrush" Color="{StaticResource TextDisabledLight}"/>
    <SolidColorBrush x:Key="TextHintLightBrush" Color="{StaticResource TextHintLight}"/>

    <!-- 深色主题文本画刷 -->
    <SolidColorBrush x:Key="TextPrimaryDarkBrush" Color="{StaticResource TextPrimaryDark}"/>
    <SolidColorBrush x:Key="TextSecondaryDarkBrush" Color="{StaticResource TextSecondaryDark}"/>
    <SolidColorBrush x:Key="TextDisabledDarkBrush" Color="{StaticResource TextDisabledDark}"/>
    <SolidColorBrush x:Key="TextHintDarkBrush" Color="{StaticResource TextHintDark}"/>

    <!-- 通用文本画刷别名（默认为浅色主题，可通过主题切换动态更改） -->
    <SolidColorBrush x:Key="TextPrimaryBrush" Color="{StaticResource TextPrimaryLight}"/>
    <SolidColorBrush x:Key="TextSecondaryBrush" Color="{StaticResource TextSecondaryLight}"/>
    <SolidColorBrush x:Key="TextDisabledBrush" Color="{StaticResource TextDisabledLight}"/>
    <SolidColorBrush x:Key="TextHintBrush" Color="{StaticResource TextHintLight}"/>

    <!-- ================================ -->
    <!-- 背景画刷 (Background Brushes)     -->
    <!-- ================================ -->

    <!-- 浅色主题背景画刷 -->
    <SolidColorBrush x:Key="BackgroundPrimaryLightBrush" Color="{StaticResource BackgroundPrimaryLight}"/>
    <SolidColorBrush x:Key="BackgroundSecondaryLightBrush" Color="{StaticResource BackgroundSecondaryLight}"/>
    <SolidColorBrush x:Key="BackgroundCardLightBrush" Color="{StaticResource BackgroundCardLight}"/>
    <SolidColorBrush x:Key="BackgroundHoverLightBrush" Color="{StaticResource BackgroundHoverLight}"/>
    <SolidColorBrush x:Key="BackgroundPressedLightBrush" Color="{StaticResource BackgroundPressedLight}"/>

    <!-- 深色主题背景画刷 -->
    <SolidColorBrush x:Key="BackgroundPrimaryDarkBrush" Color="{StaticResource BackgroundPrimaryDark}"/>
    <SolidColorBrush x:Key="BackgroundSecondaryDarkBrush" Color="{StaticResource BackgroundSecondaryDark}"/>
    <SolidColorBrush x:Key="BackgroundCardDarkBrush" Color="{StaticResource BackgroundCardDark}"/>
    <SolidColorBrush x:Key="BackgroundHoverDarkBrush" Color="{StaticResource BackgroundHoverDark}"/>
    <SolidColorBrush x:Key="BackgroundPressedDarkBrush" Color="{StaticResource BackgroundPressedDark}"/>

    <!-- 通用背景画刷别名（默认为浅色主题，可通过主题切换动态更改） -->
    <SolidColorBrush x:Key="BackgroundPrimaryBrush" Color="{StaticResource BackgroundPrimaryLight}"/>
    <SolidColorBrush x:Key="BackgroundSecondaryBrush" Color="{StaticResource BackgroundSecondaryLight}"/>
    <SolidColorBrush x:Key="BackgroundCardBrush" Color="{StaticResource BackgroundCardLight}"/>
    <SolidColorBrush x:Key="BackgroundHoverBrush" Color="{StaticResource BackgroundHoverLight}"/>
    <SolidColorBrush x:Key="BackgroundPressedBrush" Color="{StaticResource BackgroundPressedLight}"/>

    <!-- ================================ -->
    <!-- 边框画刷 (Border Brushes)         -->
    <!-- ================================ -->

    <!-- 浅色主题边框画刷 -->
    <SolidColorBrush x:Key="BorderPrimaryLightBrush" Color="{StaticResource BorderPrimaryLight}"/>
    <SolidColorBrush x:Key="BorderSecondaryLightBrush" Color="{StaticResource BorderSecondaryLight}"/>
    <SolidColorBrush x:Key="BorderFocusLightBrush" Color="{StaticResource BorderFocusLight}"/>

    <!-- 深色主题边框画刷 -->
    <SolidColorBrush x:Key="BorderPrimaryDarkBrush" Color="{StaticResource BorderPrimaryDark}"/>
    <SolidColorBrush x:Key="BorderSecondaryDarkBrush" Color="{StaticResource BorderSecondaryDark}"/>
    <SolidColorBrush x:Key="BorderFocusDarkBrush" Color="{StaticResource BorderFocusDark}"/>

    <!-- 通用边框画刷别名（默认为浅色主题，可通过主题切换动态更改） -->
    <SolidColorBrush x:Key="BorderPrimaryBrush" Color="{StaticResource BorderPrimaryLight}"/>
    <SolidColorBrush x:Key="BorderSecondaryBrush" Color="{StaticResource BorderSecondaryLight}"/>
    <SolidColorBrush x:Key="BorderFocusBrush" Color="{StaticResource BorderFocusLight}"/>

    <!-- ================================ -->
    <!-- 阴影画刷 (Shadow Brushes)         -->
    <!-- ================================ -->

    <!-- 阴影画刷 -->
    <SolidColorBrush x:Key="ShadowLightBrush" Color="{StaticResource ShadowLight}"/>
    <SolidColorBrush x:Key="ShadowMediumBrush" Color="{StaticResource ShadowMedium}"/>
    <SolidColorBrush x:Key="ShadowDarkBrush" Color="{StaticResource ShadowDark}"/>

    <!-- ================================ -->
    <!-- 特殊用途画刷 (Special Brushes)    -->
    <!-- ================================ -->

    <!-- 透明画刷 -->
    <SolidColorBrush x:Key="TransparentBrush" Color="Transparent"/>

    <!-- 半透明覆盖层画刷 -->
    <SolidColorBrush x:Key="OverlayLightBrush" Color="#80FFFFFF"/>    <!-- 50%透明白色 -->
    <SolidColorBrush x:Key="OverlayDarkBrush" Color="#80000000"/>     <!-- 50%透明黑色 -->

    <!-- 分割线画刷 -->
    <SolidColorBrush x:Key="DividerLightBrush" Color="{StaticResource NeutralColor200}"/>
    <SolidColorBrush x:Key="DividerDarkBrush" Color="{StaticResource NeutralColor700}"/>
    <SolidColorBrush x:Key="DividerBrush" Color="{StaticResource NeutralColor200}"/>

    <!-- ================================ -->
    <!-- 控件状态画刷 (Control State Brushes) -->
    <!-- ================================ -->

    <!-- 按钮状态画刷 -->
    <SolidColorBrush x:Key="ButtonNormalBrush" Color="{StaticResource PrimaryColor500}"/>
    <SolidColorBrush x:Key="ButtonHoverBrush" Color="{StaticResource PrimaryColor400}"/>
    <SolidColorBrush x:Key="ButtonPressedBrush" Color="{StaticResource PrimaryColor600}"/>
    <SolidColorBrush x:Key="ButtonDisabledBrush" Color="{StaticResource NeutralColor300}"/>

    <!-- 输入控件状态画刷 -->
    <SolidColorBrush x:Key="InputNormalBrush" Color="{StaticResource BackgroundPrimaryLight}"/>
    <SolidColorBrush x:Key="InputHoverBrush" Color="{StaticResource BackgroundHoverLight}"/>
    <SolidColorBrush x:Key="InputFocusBrush" Color="{StaticResource BackgroundPrimaryLight}"/>
    <SolidColorBrush x:Key="InputDisabledBrush" Color="{StaticResource BackgroundSecondaryLight}"/>

    <!-- 选择状态画刷 -->
    <SolidColorBrush x:Key="SelectionBrush" Color="{StaticResource PrimaryColor100}"/>
    <SolidColorBrush x:Key="SelectionActiveBrush" Color="{StaticResource PrimaryColor500}"/>
    <SolidColorBrush x:Key="SelectionInactiveBrush" Color="{StaticResource NeutralColor300}"/>

</ResourceDictionary>

<!--
    FluentSystemDesign WPF控件库 - 主题管理器资源字典
    
    此文件提供主题切换功能，支持深色和浅色主题的动态切换。
    通过重新定义通用画刷别名来实现主题切换效果。
    
    主题系统特点：
    1. 支持深色和浅色主题动态切换
    2. 使用DynamicResource实现实时主题更新
    3. 保持色彩系统的一致性
    4. 提供主题切换的编程接口
    
    使用方法：
    - 在应用程序中引用此资源字典
    - 通过ThemeManager类进行主题切换
    - 控件样式使用DynamicResource引用主题画刷
-->

<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- 引用基础资源字典 -->
    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="Colors.xaml"/>
        <ResourceDictionary Source="Brushes.xaml"/>
        <!-- 默认加载浅色主题 -->
        <ResourceDictionary Source="LightTheme.xaml"/>
    </ResourceDictionary.MergedDictionaries>



</ResourceDictionary>

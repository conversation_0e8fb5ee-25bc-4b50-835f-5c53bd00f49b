﻿<Window x:Class="FluentDesignWPF.Demo.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:FluentDesignWPF.Demo"
        mc:Ignorable="d"
        Title="FluentDesignWPF 色彩系统演示"
        Height="700"
        Width="1200"
        Background="{DynamicResource ThemeBackgroundPrimaryBrush}"
        Foreground="{DynamicResource ThemeTextPrimaryBrush}">

    <Grid Margin="16">
        <StackPanel>
            <TextBlock Text="FluentDesignWPF 色彩系统演示"
                       FontSize="24"
                       FontWeight="Bold"
                       Margin="0,0,0,16"/>

            <Button x:Name="ThemeToggleButton"
                    Content="切换主题"
                    Padding="12"
                    Margin="0,0,0,16"
                    Click="ThemeToggleButton_Click"/>

            <TextBlock Text="这是一个简单的色彩系统演示。"
                       Margin="0,0,0,16"/>

            <!-- 主题色彩展示 -->
            <TextBlock Text="主题色彩 (Primary Colors)"
                       FontSize="18"
                       FontWeight="SemiBold"
                       Margin="0,0,0,8"/>

            <UniformGrid Columns="5" Margin="0,0,0,16">
                <Border Background="{StaticResource PrimaryBrush100}"
                        Height="60" Margin="2" CornerRadius="4">
                    <TextBlock Text="100"
                               HorizontalAlignment="Center"
                               VerticalAlignment="Center"
                               Foreground="Black"/>
                </Border>
                <Border Background="{StaticResource PrimaryBrush300}"
                        Height="60" Margin="2" CornerRadius="4">
                    <TextBlock Text="300"
                               HorizontalAlignment="Center"
                               VerticalAlignment="Center"
                               Foreground="White"/>
                </Border>
                <Border Background="{StaticResource PrimaryBrush500}"
                        Height="60" Margin="2" CornerRadius="4">
                    <TextBlock Text="500"
                               HorizontalAlignment="Center"
                               VerticalAlignment="Center"
                               Foreground="White"/>
                </Border>
                <Border Background="{StaticResource PrimaryBrush700}"
                        Height="60" Margin="2" CornerRadius="4">
                    <TextBlock Text="700"
                               HorizontalAlignment="Center"
                               VerticalAlignment="Center"
                               Foreground="White"/>
                </Border>
                <Border Background="{StaticResource PrimaryBrush900}"
                        Height="60" Margin="2" CornerRadius="4">
                    <TextBlock Text="900"
                               HorizontalAlignment="Center"
                               VerticalAlignment="Center"
                               Foreground="White"/>
                </Border>
            </UniformGrid>

            <!-- 语义色彩展示 -->
            <TextBlock Text="语义色彩 (Semantic Colors)"
                       FontSize="18"
                       FontWeight="SemiBold"
                       Margin="0,0,0,8"/>

            <UniformGrid Columns="4" Margin="0,0,0,16">
                <Border Background="{StaticResource SuccessBrush}"
                        Height="60" Margin="2" CornerRadius="4">
                    <TextBlock Text="Success"
                               HorizontalAlignment="Center"
                               VerticalAlignment="Center"
                               Foreground="White"/>
                </Border>
                <Border Background="{StaticResource WarningBrush}"
                        Height="60" Margin="2" CornerRadius="4">
                    <TextBlock Text="Warning"
                               HorizontalAlignment="Center"
                               VerticalAlignment="Center"
                               Foreground="Black"/>
                </Border>
                <Border Background="{StaticResource ErrorBrush}"
                        Height="60" Margin="2" CornerRadius="4">
                    <TextBlock Text="Error"
                               HorizontalAlignment="Center"
                               VerticalAlignment="Center"
                               Foreground="White"/>
                </Border>
                <Border Background="{StaticResource InfoBrush}"
                        Height="60" Margin="2" CornerRadius="4">
                    <TextBlock Text="Info"
                               HorizontalAlignment="Center"
                               VerticalAlignment="Center"
                               Foreground="White"/>
                </Border>
            </UniformGrid>

            <!-- 主题相关色彩展示 -->
            <TextBlock Text="主题相关色彩 (Theme Colors)"
                       FontSize="18"
                       FontWeight="SemiBold"
                       Margin="0,0,0,8"/>

            <UniformGrid Columns="3" Margin="0,0,0,16">
                <Border Background="{DynamicResource ThemeBackgroundPrimaryBrush}"
                        BorderBrush="{DynamicResource ThemeBorderPrimaryBrush}"
                        BorderThickness="1"
                        Height="60" Margin="2" CornerRadius="4">
                    <TextBlock Text="主背景"
                               HorizontalAlignment="Center"
                               VerticalAlignment="Center"
                               Foreground="{DynamicResource ThemeTextPrimaryBrush}"/>
                </Border>
                <Border Background="{DynamicResource ThemeBackgroundCardBrush}"
                        BorderBrush="{DynamicResource ThemeBorderPrimaryBrush}"
                        BorderThickness="1"
                        Height="60" Margin="2" CornerRadius="4">
                    <TextBlock Text="卡片背景"
                               HorizontalAlignment="Center"
                               VerticalAlignment="Center"
                               Foreground="{DynamicResource ThemeTextPrimaryBrush}"/>
                </Border>
                <Border Background="{DynamicResource ThemeBackgroundSecondaryBrush}"
                        BorderBrush="{DynamicResource ThemeBorderPrimaryBrush}"
                        BorderThickness="1"
                        Height="60" Margin="2" CornerRadius="4">
                    <TextBlock Text="次背景"
                               HorizontalAlignment="Center"
                               VerticalAlignment="Center"
                               Foreground="{DynamicResource ThemeTextPrimaryBrush}"/>
                </Border>
            </UniformGrid>
        </StackPanel>
    </Grid>
</Window>

<!--
    FluentSystemDesign WPF控件库 - 深色主题资源字典
    
    此文件定义了深色主题下的所有主题相关色彩和画刷。
    当切换到深色主题时，会使用此资源字典替换主题相关的资源。
-->

<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- 引用基础色彩定义 -->
    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="Colors.xaml"/>
    </ResourceDictionary.MergedDictionaries>

    <!-- ================================ -->
    <!-- 深色主题色彩 (Dark Theme Colors)  -->
    <!-- ================================ -->
    
    <!-- 文本色彩 -->
    <Color x:Key="ThemeTextPrimary">#FFFFFF</Color>       <!-- 主要文本（深色主题） -->
    <Color x:Key="ThemeTextSecondary">#B3FFFFFF</Color>   <!-- 次要文本（深色主题，70%透明度） -->
    <Color x:Key="ThemeTextDisabled">#4DFFFFFF</Color>    <!-- 禁用文本（深色主题，30%透明度） -->
    <Color x:Key="ThemeTextHint">#80FFFFFF</Color>        <!-- 提示文本（深色主题，50%透明度） -->

    <!-- 背景色彩 -->
    <Color x:Key="ThemeBackgroundPrimary">#121212</Color>     <!-- 主背景（深色主题） -->
    <Color x:Key="ThemeBackgroundSecondary">#1E1E1E</Color>   <!-- 次背景（深色主题） -->
    <Color x:Key="ThemeBackgroundCard">#2D2D2D</Color>        <!-- 卡片背景（深色主题） -->
    <Color x:Key="ThemeBackgroundHover">#383838</Color>       <!-- 悬浮背景（深色主题） -->
    <Color x:Key="ThemeBackgroundPressed">#424242</Color>     <!-- 按下背景（深色主题） -->

    <!-- 边框色彩 -->
    <Color x:Key="ThemeBorderPrimary">#424242</Color>         <!-- 主边框（深色主题） -->
    <Color x:Key="ThemeBorderSecondary">#383838</Color>       <!-- 次边框（深色主题） -->
    <Color x:Key="ThemeBorderFocus">#64B5F6</Color>           <!-- 焦点边框（深色主题） -->

    <!-- 特殊色彩 -->
    <Color x:Key="ThemeDivider">#616161</Color>               <!-- 分割线（深色主题） -->
    <Color x:Key="ThemeOverlay">#80000000</Color>             <!-- 覆盖层（深色主题） -->

    <!-- ================================ -->
    <!-- 深色主题画刷 (Dark Theme Brushes) -->
    <!-- ================================ -->
    
    <!-- 文本画刷 -->
    <SolidColorBrush x:Key="ThemeTextPrimaryBrush" Color="{StaticResource ThemeTextPrimary}"/>
    <SolidColorBrush x:Key="ThemeTextSecondaryBrush" Color="{StaticResource ThemeTextSecondary}"/>
    <SolidColorBrush x:Key="ThemeTextDisabledBrush" Color="{StaticResource ThemeTextDisabled}"/>
    <SolidColorBrush x:Key="ThemeTextHintBrush" Color="{StaticResource ThemeTextHint}"/>

    <!-- 背景画刷 -->
    <SolidColorBrush x:Key="ThemeBackgroundPrimaryBrush" Color="{StaticResource ThemeBackgroundPrimary}"/>
    <SolidColorBrush x:Key="ThemeBackgroundSecondaryBrush" Color="{StaticResource ThemeBackgroundSecondary}"/>
    <SolidColorBrush x:Key="ThemeBackgroundCardBrush" Color="{StaticResource ThemeBackgroundCard}"/>
    <SolidColorBrush x:Key="ThemeBackgroundHoverBrush" Color="{StaticResource ThemeBackgroundHover}"/>
    <SolidColorBrush x:Key="ThemeBackgroundPressedBrush" Color="{StaticResource ThemeBackgroundPressed}"/>

    <!-- 边框画刷 -->
    <SolidColorBrush x:Key="ThemeBorderPrimaryBrush" Color="{StaticResource ThemeBorderPrimary}"/>
    <SolidColorBrush x:Key="ThemeBorderSecondaryBrush" Color="{StaticResource ThemeBorderSecondary}"/>
    <SolidColorBrush x:Key="ThemeBorderFocusBrush" Color="{StaticResource ThemeBorderFocus}"/>

    <!-- 特殊画刷 -->
    <SolidColorBrush x:Key="ThemeDividerBrush" Color="{StaticResource ThemeDivider}"/>
    <SolidColorBrush x:Key="ThemeOverlayBrush" Color="{StaticResource ThemeOverlay}"/>

    <!-- ================================ -->
    <!-- 控件状态画刷 (Control State Brushes) -->
    <!-- ================================ -->
    
    <!-- 输入控件状态画刷 -->
    <SolidColorBrush x:Key="InputNormalBrush" Color="#121212"/>      <!-- 输入框正常状态 -->
    <SolidColorBrush x:Key="InputHoverBrush" Color="#383838"/>       <!-- 输入框悬浮状态 -->
    <SolidColorBrush x:Key="InputFocusBrush" Color="#121212"/>       <!-- 输入框焦点状态 -->
    <SolidColorBrush x:Key="InputDisabledBrush" Color="#1E1E1E"/>    <!-- 输入框禁用状态 -->

</ResourceDictionary>

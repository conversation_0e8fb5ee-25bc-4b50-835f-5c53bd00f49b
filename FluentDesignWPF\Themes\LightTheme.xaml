<!--
    FluentSystemDesign WPF控件库 - 浅色主题资源字典
    
    此文件定义了浅色主题下的所有主题相关色彩和画刷。
    当切换到浅色主题时，会使用此资源字典替换主题相关的资源。
-->

<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- 引用基础色彩定义 -->
    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="Colors.xaml"/>
    </ResourceDictionary.MergedDictionaries>

    <!-- ================================ -->
    <!-- 浅色主题色彩 (Light Theme Colors) -->
    <!-- ================================ -->
    
    <!-- 文本色彩 -->
    <Color x:Key="ThemeTextPrimary">#212121</Color>      <!-- 主要文本（浅色主题） -->
    <Color x:Key="ThemeTextSecondary">#757575</Color>    <!-- 次要文本（浅色主题） -->
    <Color x:Key="ThemeTextDisabled">#BDBDBD</Color>     <!-- 禁用文本（浅色主题） -->
    <Color x:Key="ThemeTextHint">#9E9E9E</Color>         <!-- 提示文本（浅色主题） -->

    <!-- 背景色彩 -->
    <Color x:Key="ThemeBackgroundPrimary">#FFFFFF</Color>    <!-- 主背景（浅色主题） -->
    <Color x:Key="ThemeBackgroundSecondary">#FAFAFA</Color>  <!-- 次背景（浅色主题） -->
    <Color x:Key="ThemeBackgroundCard">#FFFFFF</Color>       <!-- 卡片背景（浅色主题） -->
    <Color x:Key="ThemeBackgroundHover">#F5F5F5</Color>      <!-- 悬浮背景（浅色主题） -->
    <Color x:Key="ThemeBackgroundPressed">#EEEEEE</Color>    <!-- 按下背景（浅色主题） -->

    <!-- 边框色彩 -->
    <Color x:Key="ThemeBorderPrimary">#E0E0E0</Color>        <!-- 主边框（浅色主题） -->
    <Color x:Key="ThemeBorderSecondary">#EEEEEE</Color>      <!-- 次边框（浅色主题） -->
    <Color x:Key="ThemeBorderFocus">#2196F3</Color>          <!-- 焦点边框（浅色主题） -->

    <!-- 特殊色彩 -->
    <Color x:Key="ThemeDivider">#EEEEEE</Color>              <!-- 分割线（浅色主题） -->
    <Color x:Key="ThemeOverlay">#80FFFFFF</Color>            <!-- 覆盖层（浅色主题） -->

    <!-- ================================ -->
    <!-- 浅色主题画刷 (Light Theme Brushes) -->
    <!-- ================================ -->
    
    <!-- 文本画刷 -->
    <SolidColorBrush x:Key="ThemeTextPrimaryBrush" Color="{StaticResource ThemeTextPrimary}"/>
    <SolidColorBrush x:Key="ThemeTextSecondaryBrush" Color="{StaticResource ThemeTextSecondary}"/>
    <SolidColorBrush x:Key="ThemeTextDisabledBrush" Color="{StaticResource ThemeTextDisabled}"/>
    <SolidColorBrush x:Key="ThemeTextHintBrush" Color="{StaticResource ThemeTextHint}"/>

    <!-- 背景画刷 -->
    <SolidColorBrush x:Key="ThemeBackgroundPrimaryBrush" Color="{StaticResource ThemeBackgroundPrimary}"/>
    <SolidColorBrush x:Key="ThemeBackgroundSecondaryBrush" Color="{StaticResource ThemeBackgroundSecondary}"/>
    <SolidColorBrush x:Key="ThemeBackgroundCardBrush" Color="{StaticResource ThemeBackgroundCard}"/>
    <SolidColorBrush x:Key="ThemeBackgroundHoverBrush" Color="{StaticResource ThemeBackgroundHover}"/>
    <SolidColorBrush x:Key="ThemeBackgroundPressedBrush" Color="{StaticResource ThemeBackgroundPressed}"/>

    <!-- 边框画刷 -->
    <SolidColorBrush x:Key="ThemeBorderPrimaryBrush" Color="{StaticResource ThemeBorderPrimary}"/>
    <SolidColorBrush x:Key="ThemeBorderSecondaryBrush" Color="{StaticResource ThemeBorderSecondary}"/>
    <SolidColorBrush x:Key="ThemeBorderFocusBrush" Color="{StaticResource ThemeBorderFocus}"/>

    <!-- 特殊画刷 -->
    <SolidColorBrush x:Key="ThemeDividerBrush" Color="{StaticResource ThemeDivider}"/>
    <SolidColorBrush x:Key="ThemeOverlayBrush" Color="{StaticResource ThemeOverlay}"/>

    <!-- ================================ -->
    <!-- 控件状态画刷 (Control State Brushes) -->
    <!-- ================================ -->
    
    <!-- 输入控件状态画刷 -->
    <SolidColorBrush x:Key="InputNormalBrush" Color="#FFFFFF"/>      <!-- 输入框正常状态 -->
    <SolidColorBrush x:Key="InputHoverBrush" Color="#F5F5F5"/>       <!-- 输入框悬浮状态 -->
    <SolidColorBrush x:Key="InputFocusBrush" Color="#FFFFFF"/>       <!-- 输入框焦点状态 -->
    <SolidColorBrush x:Key="InputDisabledBrush" Color="#FAFAFA"/>    <!-- 输入框禁用状态 -->

</ResourceDictionary>

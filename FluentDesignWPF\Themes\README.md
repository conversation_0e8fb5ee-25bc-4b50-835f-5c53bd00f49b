# FluentDesignWPF 色彩系统

FluentDesignWPF 控件库提供了一套完整的色彩系统，基于 Microsoft Fluent Design System 设计语言，支持深色和浅色主题的动态切换。

## 🎨 色彩系统概述

### 色彩分类

1. **主题色彩 (Primary Colors)** - 蓝色系，用于主要操作按钮、链接、选中状态
2. **辅助色彩 (Secondary Colors)** - 青色系，用于辅助信息、次要操作
3. **强调色彩 (Accent Colors)** - 橙色系，用于警告、通知、特殊强调
4. **中性色彩 (Neutral Colors)** - 灰色系，用于文本、边框、背景
5. **语义色彩 (Semantic Colors)** - 成功(绿)、警告(黄)、错误(红)、信息(蓝)
6. **主题相关色彩** - 根据当前主题(深色/浅色)动态变化的色彩

### 色彩层级

每种色彩都提供了完整的层级（50-900），从最浅到最深：
- **50-200**: 浅色调，用于背景高亮、悬浮状态
- **300-400**: 中浅色调，用于次要元素
- **500**: 标准色调，最常用的主色
- **600-700**: 中深色调，用于活跃状态
- **800-900**: 深色调，用于强调和对比

## 📁 文件结构

```
FluentDesignWPF/Themes/
├── Colors.xaml          # 色彩定义
├── Brushes.xaml         # 画刷资源
├── ThemeManager.xaml    # 主题管理器资源
├── ThemeManager.cs      # 主题管理器类
├── Generic.xaml         # 通用资源字典
└── README.md           # 本文档
```

## 🚀 快速开始

### 1. 引用资源字典

在您的 WPF 应用程序的 `App.xaml` 中添加：

```xml
<Application.Resources>
    <ResourceDictionary>
        <ResourceDictionary.MergedDictionaries>
            <ResourceDictionary Source="pack://application:,,,/FluentDesignWPF;component/Themes/Generic.xaml"/>
        </ResourceDictionary.MergedDictionaries>
    </ResourceDictionary>
</Application.Resources>
```

### 2. 初始化主题系统

在您的应用程序启动代码中：

```csharp
using FluentDesignWPF.Themes;

// 在 App.xaml.cs 或 MainWindow 构造函数中
ThemeManager.Initialize(Theme.Light); // 初始化为浅色主题
```

### 3. 使用色彩资源

在 XAML 中使用色彩：

```xml
<!-- 使用固定色彩 -->
<Button Background="{StaticResource PrimaryBrush500}" 
        Foreground="{StaticResource WhiteBrush}"/>

<!-- 使用主题相关色彩（推荐） -->
<TextBlock Foreground="{StaticResource ThemeTextPrimaryBrush}"/>
<Border Background="{StaticResource ThemeBackgroundCardBrush}"/>
```

## 🎯 主要色彩资源

### 主题色彩
- `PrimaryBrush` / `PrimaryBrush500` - 主色调
- `PrimaryLightBrush` / `PrimaryBrush300` - 浅色主色调
- `PrimaryDarkBrush` / `PrimaryBrush700` - 深色主色调

### 语义色彩
- `SuccessBrush` - 成功色 (绿色)
- `WarningBrush` - 警告色 (黄色)
- `ErrorBrush` - 错误色 (红色)
- `InfoBrush` - 信息色 (蓝色)

### 主题相关色彩（推荐使用）
- `ThemeTextPrimaryBrush` - 主要文本色
- `ThemeTextSecondaryBrush` - 次要文本色
- `ThemeBackgroundPrimaryBrush` - 主背景色
- `ThemeBackgroundCardBrush` - 卡片背景色
- `ThemeBorderPrimaryBrush` - 主边框色

## 🌓 主题切换

### 程序化切换主题

```csharp
using FluentDesignWPF.Themes;

// 切换到深色主题
ThemeManager.SetTheme(Theme.Dark);

// 切换到浅色主题
ThemeManager.SetTheme(Theme.Light);

// 在深色和浅色之间切换
ThemeManager.ToggleTheme();

// 检查当前主题
if (ThemeManager.IsDarkTheme)
{
    // 当前是深色主题
}
```

### 监听主题变化

```csharp
// 订阅主题变化事件
ThemeManager.ThemeChanged += (sender, e) =>
{
    Console.WriteLine($"主题从 {e.OldTheme} 切换到 {e.NewTheme}");
};
```

## 🎨 色彩使用指南

### 文本色彩
- **主要文本**: 使用 `ThemeTextPrimaryBrush`
- **次要文本**: 使用 `ThemeTextSecondaryBrush`
- **禁用文本**: 使用 `ThemeTextDisabledBrush`
- **提示文本**: 使用 `ThemeTextHintBrush`

### 背景色彩
- **主背景**: 使用 `ThemeBackgroundPrimaryBrush`
- **次背景**: 使用 `ThemeBackgroundSecondaryBrush`
- **卡片背景**: 使用 `ThemeBackgroundCardBrush`
- **悬浮背景**: 使用 `ThemeBackgroundHoverBrush`

### 边框色彩
- **主边框**: 使用 `ThemeBorderPrimaryBrush`
- **次边框**: 使用 `ThemeBorderSecondaryBrush`
- **焦点边框**: 使用 `ThemeBorderFocusBrush`

### 状态色彩
- **成功状态**: 使用 `SuccessBrush` 系列
- **警告状态**: 使用 `WarningBrush` 系列
- **错误状态**: 使用 `ErrorBrush` 系列
- **信息状态**: 使用 `InfoBrush` 系列

## 🔧 自定义色彩

### 添加自定义色彩

在您的应用程序资源中添加：

```xml
<Application.Resources>
    <ResourceDictionary>
        <ResourceDictionary.MergedDictionaries>
            <ResourceDictionary Source="pack://application:,,,/FluentDesignWPF;component/Themes/Generic.xaml"/>
        </ResourceDictionary.MergedDictionaries>
        
        <!-- 自定义色彩 -->
        <Color x:Key="CustomColor">#FF6B46C1</Color>
        <SolidColorBrush x:Key="CustomBrush" Color="{StaticResource CustomColor}"/>
    </ResourceDictionary>
</Application.Resources>
```

### 覆盖默认色彩

```xml
<!-- 覆盖主色调 -->
<Color x:Key="PrimaryColor500">#FF9333EA</Color>
```

## 📱 响应式设计

色彩系统支持不同的显示环境：
- **高对比度模式**: 自动调整色彩对比度
- **不同DPI设置**: 色彩在不同DPI下保持一致
- **无障碍支持**: 符合WCAG对比度标准

## 🎯 最佳实践

1. **优先使用主题相关色彩**: 使用 `Theme*Brush` 系列资源以支持主题切换
2. **保持色彩一致性**: 在同一应用中使用统一的色彩体系
3. **注意对比度**: 确保文本和背景有足够的对比度
4. **语义化使用**: 正确使用语义色彩表达不同的状态和含义
5. **测试主题切换**: 确保在深色和浅色主题下都有良好的视觉效果

## 🔍 示例应用

运行 `FluentDesignWPF.Demo` 项目查看完整的色彩系统演示，包括：
- 所有色彩的可视化展示
- 主题切换功能演示
- 不同控件状态的色彩应用
- 响应式布局示例

## 📚 相关资源

- [Microsoft Fluent Design System](https://www.microsoft.com/design/fluent)
- [WPF 资源字典文档](https://docs.microsoft.com/zh-cn/dotnet/desktop/wpf/advanced/xaml-resources)
- [色彩无障碍指南](https://www.w3.org/WAI/WCAG21/Understanding/contrast-minimum.html)
